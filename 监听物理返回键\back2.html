<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）</div>

    <script>
      // iOS Safari 兼容的返回键拦截方案
      ;(function () {
        let isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
        let isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
        let userActivated = false
        let historyLength = 0
        let hashChangeBlocked = false

        console.log("设备检测:", { isIOS, isSafari })

        // 检测用户激活状态
        function markUserActivated() {
          if (!userActivated) {
            userActivated = true
            console.log("用户已激活")
            // 用户激活后建立更多历史记录
            if (!isIOS || !isSafari) {
              for (let i = 0; i < 20; i++) {
                try {
                  history.pushState({ index: ++historyLength }, null, location.href)
                } catch (e) {
                  break
                }
              }
            }
          }
        }

        // iOS Safari 专用方案：使用 hash 拦截
        function setupIOSSafariProtection() {
          // 设置初始 hash
          if (!location.hash) {
            location.hash = "no-back"
          }

          // 监听 hashchange 事件
          window.addEventListener("hashchange", function (e) {
            if (!hashChangeBlocked) {
              console.log("Hash 变化被拦截")
              hashChangeBlocked = true
              // 恢复 hash
              setTimeout(() => {
                location.hash = "no-back"
                hashChangeBlocked = false
              }, 1)
              return false
            }
          })

          // 监听 pagehide 事件（iOS 特有）
          window.addEventListener("pagehide", function (e) {
            console.log("页面隐藏事件")
            if (e.persisted) {
              e.preventDefault()
              return false
            }
          })

          // 监听 beforeunload 事件
          window.addEventListener("beforeunload", function (e) {
            console.log("页面卸载事件")
            e.preventDefault()
            e.returnValue = ""
            return ""
          })
        }

        // 通用方案：使用 history API
        function setupGeneralProtection() {
          // 建立初始历史栈
          for (let i = 0; i < 50; i++) {
            try {
              history.pushState({ index: ++historyLength }, null, location.href)
            } catch (e) {
              break
            }
          }

          // 监听 popstate 事件
          window.addEventListener("popstate", function (e) {
            console.log("Popstate 事件被拦截")
            if (userActivated) {
              // 用户已激活，可以安全使用 pushState
              history.pushState({ index: ++historyLength }, null, location.href)
            } else {
              // 用户未激活，使用替代方案
              setTimeout(() => {
                try {
                  history.pushState({ index: ++historyLength }, null, location.href)
                } catch (err) {
                  // 静默失败
                }
              }, 0)
            }
          })
        }

        // 根据浏览器类型选择方案
        if (isIOS && isSafari) {
          console.log("使用 iOS Safari 专用方案")
          setupIOSSafariProtection()
        } else {
          console.log("使用通用方案")
          setupGeneralProtection()
        }

        // 监听用户交互事件
        const userEvents = ["click", "touchstart", "touchend", "mousedown", "keydown", "scroll"]
        userEvents.forEach(eventType => {
          document.addEventListener(eventType, markUserActivated, {
            once: false,
            passive: true,
            capture: true
          })
        })

        // 阻止 Backspace 键导航
        document.addEventListener(
          "keydown",
          function (e) {
            if (e.key === "Backspace" || e.keyCode === 8) {
              if (!["INPUT", "TEXTAREA"].includes(e.target.tagName)) {
                e.preventDefault()
                e.stopPropagation()
                markUserActivated()
                return false
              }
            }
          },
          { capture: true }
        )

        // 页面获得焦点时重新建立保护
        window.addEventListener("focus", function () {
          if (userActivated && (!isIOS || !isSafari)) {
            for (let i = 0; i < 10; i++) {
              try {
                history.pushState({ index: ++historyLength }, null, location.href)
              } catch (e) {
                break
              }
            }
          }
        })

        // 页面可见性变化时的处理
        document.addEventListener("visibilitychange", function () {
          if (!document.hidden && userActivated && (!isIOS || !isSafari)) {
            for (let i = 0; i < 10; i++) {
              try {
                history.pushState({ index: ++historyLength }, null, location.href)
              } catch (e) {
                break
              }
            }
          }
        })

        // 定时器维护历史栈（仅在用户激活后且非iOS Safari）
        if (!isIOS || !isSafari) {
          setInterval(function () {
            if (userActivated) {
              try {
                history.pushState({ index: ++historyLength }, null, location.href)
              } catch (e) {
                // 静默失败
              }
            }
          }, 1000)
        }
      })()

      const app = new Vue({
        el: "#app",
        mounted() {},
        methods: {}
      })
    </script>
  </body>
</html>
