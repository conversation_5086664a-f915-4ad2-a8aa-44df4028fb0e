<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）</div>

    <script>
      // 简单的返回键拦截方案 + iOS Safari 兼容
      ;(function () {
        // 检测是否为 iOS Safari
        let isIOSSafari =
          /iPad|iPhone|iPod/.test(navigator.userAgent) &&
          /^((?!chrome|android).)*safari/i.test(navigator.userAgent) &&
          !window.MSStream

        if (isIOSSafari) {
          // iOS Safari 专用方案：使用 hash 拦截
          console.log("检测到 iOS Safari，使用 hash 拦截方案")

          // 设置初始 hash
          if (!location.hash) {
            location.hash = "no-back"
          }

          // 监听 hashchange 事件拦截返回
          window.addEventListener("hashchange", function () {
            location.hash = "no-back"
          })
        } else {
          // 其他浏览器使用原有方案
          console.log("使用标准 history API 方案")

          // 建立历史栈
          for (let i = 0; i < 10; i++) {
            history.pushState({ step: i }, "", location.href)
          }

          // 监听 popstate 事件
          window.addEventListener("popstate", function () {
            history.pushState({ step: Date.now() }, "", location.href)
          })
        }
      })()

      const app = new Vue({
        el: "#app",
        mounted() {},
        methods: {}
      })
    </script>
  </body>
</html>
